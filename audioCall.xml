<?xml version="1.0" encoding="ISO-8859-1" ?>
<!DOCTYPE scenario SYSTEM "sipp.dtd">

<scenario name="Audio Call Test">
  
  <!-- SIP INVITE to initiate the call -->
  <send retrans="500">
    <![CDATA[
      INVITE sip:00001234@[remote_ip]:[remote_port] SIP/2.0
      Via: SIP/2.0/[transport] [local_ip]:[local_port];branch=[branch]
      From: <sip:[field0]@[local_ip]:[local_port]>;tag=[pid]SIPpTag00[call_number]
      To:  <sip:00001234@[remote_ip]:[remote_port]>
      Call-ID: [call_id]
      CSeq: 1 INVITE
      Contact: sip:[field0]@[local_ip]:[local_port]
      Max-Forwards: 70
      Subject: Audio Test
      Content-Type: application/sdp
      Content-Length: [len]

      v=0
      o=user1 53655765 2353687637 IN IP[local_ip_type] [local_ip]
      s=-
      c=IN IP[media_ip_type] [media_ip]
      t=0 0
      m=audio [media_port] RTP/AVP 0
      a=rtpmap:0 PCMU/8000
    ]]>
  </send>

  <!-- Wait for the 200 OK response from the server -->
  <recv response="200" rtd="true">
  </recv>

  <!-- Send ACK to acknowledge the call -->
  <send>
    <![CDATA[
      ACK sip:00001234@[remote_ip]:[remote_port] SIP/2.0
      Via: SIP/2.0/[transport] [local_ip]:[local_port];branch=[branch]
      From: <sip:[field0]@[local_ip]:[local_port]>;tag=[pid]SIPpTag00[call_number]
      To: <sip:00001234@[remote_ip]:[remote_port]>[peer_tag_param]
      Call-ID: [call_id]
      CSeq: 1 ACK
      Contact: sip:[field0]@[local_ip]:[local_port]
      Max-Forwards: 70
      Subject: Audio Test
      Content-Length: 0
    ]]>
  </send>

  <!-- RTP Echo: Send and receive audio -->
  <nop>
    <action>
      <exec play_pcap_audio="/root/sipp_new/audio.pcap"/>
    </action>
  </nop>

  <!-- Wait for 5 minutes (300000 ms) before terminating the call -->
  <pause milliseconds="300000"/>

  <!-- Send BYE to terminate the call -->
  <send retrans="500">
    <![CDATA[
      BYE sip:00001234@[remote_ip]:[remote_port] SIP/2.0
      Via: SIP/2.0/[transport] [local_ip]:[local_port];branch=[branch]
      From: <sip:[field0]@[local_ip]:[local_port]>;tag=[pid]SIPpTag00[call_number]
      To:  <sip:00001234@[remote_ip]:[remote_port]>[peer_tag_param]
      Call-ID: [call_id]
      CSeq: 2 BYE
      Contact: sip:[field0]@[local_ip]:[local_port]
      Max-Forwards: 70
      Subject: Audio Test
      Content-Length: 0
    ]]>
  </send>

  <!-- Receive 200 OK response for BYE -->
  <recv response="200" crlf="true">
  </recv>

</scenario>

